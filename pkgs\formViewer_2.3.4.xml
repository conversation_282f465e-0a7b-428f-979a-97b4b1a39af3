<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/formViewer" name="formViewer" jsClass="AppMagic.Controls.Form.FormControl" version="2.3.4" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[FormViewer
      Control description here.]]></description>
  <requires>
    <require type="javascript" src="/ctrllib/common/js/container.js" />
    <require type="javascript" src="/ctrllib/fluidGrid/js/fluidGrid.js" />
    <require type="javascript" src="/ctrllib/form/js/form.js" />
  </requires>
  <appMagic:capabilities supportsNestedControls="true" replicatesNestedControls="true" contextualViewsEnabled="false" autoBorders="true" autoFill="true" autoPointerViewState="false" autoDisabledViewState="false" autoFocusedBorders="true" defaultLayoutName="vertical" aggregatesChildLayouts="true" replicationLimit="0" isVersionFlexible="true" managesNestedControlBounds="true" />
  <!-- The parents that are supported. All other controls are prohibited as parents for this control. -->
  <appMagic:supportedParents>
    <appMagic:supportedControl name="screen" />
    <appMagic:supportedControl name="groupContainer" />
  </appMagic:supportedParents>
  <!-- The children that are supported. All other controls are prohibited as children for this control. -->
  <appMagic:supportedChildren>
    <appMagic:supportedControl name="dataCard" />
    <appMagic:supportedControl name="typedDataCard" />
  </appMagic:supportedChildren>
  <properties>
    <!-- Data Properties -->
    <!--DataSource cannot be an in/out property with passthrough reference as doing that makes it lose the app datasource reference-->
    <!-- DataSource only requires metadata. No actual data is needed. Hence we mark it as supportsPaging = true. Codegen makes use of this property to generate optimized code.-->
    <property name="DataSource" localizedName="##formProperties_DataSource##" datatype="Array" isPrimaryInputProperty="true" supportsPaging="true" direction="in">
      <appMagic:thisItemInput></appMagic:thisItemInput>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##formProperties_DataSource_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##formProperties_DataSource_Tooltip##</appMagic:tooltip>
      <!--Tells that this property provides us with the datasource value. This is currently used in conjunction with column name reference
      property to create namemaps based on metadata-->
      <appMagic:dataSourceProperty />
    </property>
    <property name="Item" localizedName="##formProperties_Item##" datatype="Object" isTypeBoundToPrimaryInput="true" direction="in">
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##formProperties_Item_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##formProperties_Item_Tooltip##</appMagic:tooltip>
    </property>
    <property name="DisplayMode" localizedName="##formProperties_DisplayMode##" datatype="DisplayMode" direction="out">
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##formProperties_DisplayMode_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##formProperties_DisplayMode_Tooltip##</appMagic:tooltip>
    </property>
    <!-- Current property is hidden per PM decision. Codegen uses this property to allow reachability into children of form. -->
    <property name="Current" localizedName="##formProperties_Current##" datatype="Object" indicatesActiveItem="true" direction="out" hidden="true">
      <appMagic:category>data</appMagic:category>
      <appMagic:passThroughReference>Item</appMagic:passThroughReference>
      <appMagic:nestedAware />
    </property>
  </properties>
  <appMagic:includeProperties>
    <appMagic:includeProperty name="ContentLanguage" />
    <appMagic:includeProperty name="Fill" />
    <appMagic:includeProperty name="BorderColor" />
    <appMagic:includeProperty name="BorderStyle" />
    <appMagic:includeProperty name="BorderThickness" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" defaultValue="4" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Width" defaultValue="800" phoneDefaultValue="640" />
    <appMagic:includeProperty name="Height" defaultValue="500" />
    <appMagic:includeProperty name="NumberOfColumns" defaultValue="3" phoneDefaultValue="1" />
    <appMagic:includeProperty name="SnapToColumns" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="AcceptsFocus" />
    <!-- Hidden properties -->
    <appMagic:includeProperty name="minimumWidth" defaultValue="20" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="20" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="600" />
    <appMagic:includeProperty name="maximumHeight" defaultValue="300" />
    <appMagic:includeProperty name="TabIndex" defaultValue="-1" hidden="true" />
  </appMagic:includeProperties>
  <appMagic:propertyDependencies>
    <appMagic:propertyDependency input="DataSource" output="Current" />
    <appMagic:propertyDependency input="Item" output="Current" />
    <appMagic:propertyDependency input="AcceptsFocus" output="TabIndex" />
  </appMagic:propertyDependencies>
  <appMagic:insertMetadata isDeviceOptimized="true">
    <appMagic:category name="Input" priority="160" />
  </appMagic:insertMetadata>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="DataSource" serverProvidesValue="true" computedValueType="ConnectedDataSourceName" showInCommandBar="true" showInCanvasInlineActionBar="true" />
      <appMagic:dataSourceSelectionCallout dataSourcePropertyName="DataSource" />
      <appMagic:configureCdsViews propertyToReplace="DataSource" />
      <appMagic:wizardPropertyGroup>
        <appMagic:wizardStep label="##ControlSidebar_PropertiesPanel_Fields_DisplayName##" panelTitle="##ControlSidebar_PropertiesPanel_Fields_DisplayName##" linkedLocation="FieldSelection" computedValueType="FieldSummaryEditFields" propertyInvariantName="Fields" serverProvidesValue="true" showInCommandBar="true" showInCanvasInlineActionBar="true" />
      </appMagic:wizardPropertyGroup>
      <appMagic:configureFields propertyInvariantName="ShowFields" dataSourcePropertyName="DataSource" supportsCollection="false" isEditable="false" displayFormat="PropertiesDisplayOrder" generateFieldSelectionConfig="true" addCustomFieldType="blankCard" fieldOrderingType="XYProperty" unsupportedFieldTypes="P" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="SnapToColumns" showOnlyInPropertyPanel="true" promptUserBeforeChanging="true" promptUserTitle="##ControlSidebar_PropertiesPanel_ConfirmDialogMessage_SnapToColumns_Title##" promptUserBody="##ControlSidebar_PropertiesPanel_ConfirmDialogMessage_SnapToColumns_Body##" promptUserOkButtonText="##ControlSidebar_PropertiesPanel_ConfirmDialogMessage_SnapToColumns_OkButtonText##" promptUserOnValue="true" />
      <appMagic:property name="NumberOfColumns" showOnlyInPropertyPanel="true" displayType="NumberOfColumnsEnum" />
      <appMagic:serverProperty name="ControlLayout" label="##ControlSidebar_PropertiesPanel_Layout_DisplayName##" serverProvidesValue="true" computedValueType="ControlLayout" propertyInvariantName="ControlLayout" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="Color">
        <appMagic:property name="Fill" showInCommandBar="true" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" />
        <appMagic:property name="BorderThickness" />
        <appMagic:property name="BorderColor" />
      </appMagic:propertyGroup>
      <appMagic:property name="AcceptsFocus" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:conversion from="2.0.0" to="2.0.1">
    <!-- legacy KO template was removed -->
  </appMagic:conversion>
  <appMagic:conversion from="2.0.1" to="2.0.2">
    <!-- class namespace renamed and required resources updated -->
  </appMagic:conversion>
  <appMagic:conversion from="2.0.2" to="2.1.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
  <appMagic:conversion from="2.1.0" to="2.2.0">
    <appMagic:conversionAction type="add" name="AcceptsFocus" />
    <appMagic:conversionAction type="add" name="FocusedBorderColor" />
    <appMagic:conversionAction type="add" name="FocusedBorderThickness" />
    <appMagic:conversionAction type="add" name="TabIndex" />
  </appMagic:conversion>
  <appMagic:conversion from="2.2.0" to="2.3.0">
    <!-- Adding showInCommandBar flag -->
  </appMagic:conversion>
  <appMagic:conversion from="2.3.0" to="2.3.1">
    <!-- Adding showInCommandBar flag to additional properties -->
  </appMagic:conversion>
  <appMagic:conversion from="2.3.1" to="2.3.2">
    <!-- Adding showInCommandBar flag to Fields property -->
  </appMagic:conversion>
  <appMagic:conversion from="2.3.2" to="2.3.3">
    <!-- Removing hideInRecordingMode property attribute -->
  </appMagic:conversion>
  <appMagic:conversion from="2.3.3" to="2.3.4">
    <!-- Adding showInCanvasInlineActionBar flag-->
  </appMagic:conversion>
</widget>